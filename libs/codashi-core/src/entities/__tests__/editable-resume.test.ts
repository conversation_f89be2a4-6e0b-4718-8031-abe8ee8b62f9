import { describe, expect, it } from 'vitest';

import { yamlToEditableResume } from '../resume.yaml';

describe('editableResume schema', () => {
  it('should successfully parse valid YAML to EditableResume object', () => {
    const validYaml = `
      header:
        - name: "name"
          value: "<PERSON>"
        - name: "title"
          value: "Software Engineer"
        - name: "email"
          value: "<EMAIL>"
        - name: "phone"
          value: null
      sections:
        - name: "work"
          items:
            - name: "Tech Corp"
              position: "Senior Developer"
              start_date: "2020-01-01"
              end_date: "2023-01-01"
              highlights:
                - "Led team of 5 developers"
                - "Implemented CI/CD pipeline"
        - name: "skills"
          items:
            - name: "JavaScript"
              level: "Expert"
              keywords:
                - "React"
                - "Node.js"
                - "TypeScript"
    `;

    const result = yamlToEditableResume(validYaml);

    if (!result.success) {
      console.log('Validation errors:', result.errors);
    }

    expect(result.success).toBe(true);

    if (result.success) {
      expect(result.data).toEqual({
        header: [
          {
            name: 'name',
            value: '<PERSON>',
          },
          {
            name: 'title',
            value: 'Software Engineer',
          },
          {
            name: 'email',
            value: '<EMAIL>',
          },
          {
            name: 'phone',
            value: null,
          },
        ],
        sections: [
          {
            name: 'work',
            items: [
              {
                name: 'Tech Corp',
                location: null,
                description: null,
                position: 'Senior Developer',
                url: null,
                start_date: '2020-01-01',
                end_date: '2023-01-01',
                summary: null,
                highlights: [
                  'Led team of 5 developers',
                  'Implemented CI/CD pipeline',
                ],
                positions: null,
              },
            ],
          },
          {
            name: 'skills',
            items: [
              {
                name: 'JavaScript',
                level: 'Expert',
                keywords: ['React', 'Node.js', 'TypeScript'],
              },
            ],
          },
        ],
      });
    }
  });
});
