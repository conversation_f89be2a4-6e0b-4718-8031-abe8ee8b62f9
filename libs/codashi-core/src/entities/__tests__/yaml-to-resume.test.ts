import { describe, expect, it } from 'vitest';

import { yamlToResume } from '../resume.yaml';

describe('yamlToResume', () => {
  it('should successfully parse valid YAML to Resume object', () => {
    const validYaml = `
      basics:
        name: <PERSON>
        email: <EMAIL>
        summary: A professional software developer
    `;

    const result = yamlToResume(validYaml);

    expect(result).toEqual({
      success: true,
      data: expect.objectContaining({
        basics: expect.objectContaining({
          name: '<PERSON>',
          email: '<EMAIL>',
          summary: 'A professional software developer',
        }),
      }),
    });
  });

  it('should throw ParsingError when YAML is invalid according to Resume schema', () => {
    const invalidYaml = `
      basics:
        name: <PERSON>
        email: not-an-email
    `;

    // Act & Assert
    const result = yamlToResume(invalidYaml);

    expect(result.success).toBe(false);
  });

  it('should throw ParsingError when input is not valid YAML', () => {
    const invalidYaml = `
      basics:
        name: "Unclosed string
        email: <EMAIL>
    `;

    // Act & Assert
    expect(yamlToResume(invalidYaml).success).toBe(false);
  });

  it('should handle empty YAML input', () => {
    const emptyYaml = '';

    // The actual implementation throws an error for empty input
    // since it doesn't match the schema requirements
    // Act & Assert
    expect(yamlToResume(emptyYaml).success).toBe(false);
  });

  it('should handle YAML with complex nested structures', () => {
    const complexYaml = `
      basics:
        name: John Doe
        email: <EMAIL>
        profiles:
          - network: GitHub
            username: johndoe
            url: https://github.com/johndoe
          - network: LinkedIn
            username: johndoe
            url: https://linkedin.com/in/johndoe
      work:
        - name: Company A
          position: Senior Developer
          start_date: "2020-01-01"
          end_date: "2023-01-01"
          highlights:
            - Led team of 5 developers
            - Implemented CI/CD pipeline
        - name: Company B
          position: Developer
          start_date: "2018-01-01"
          end_date: "2020-01-01"
    `;

    const result = yamlToResume(complexYaml);

    if (!result.success) {
      console.log(result.errors);
    }

    expect(result).toMatchObject({
      success: true,
      data: {
        basics: {
          name: 'John Doe',
          email: '<EMAIL>',
          profiles: [
            {
              network: 'GitHub',
              username: 'johndoe',
              url: 'https://github.com/johndoe',
            },
            {
              network: 'LinkedIn',
              username: 'johndoe',
              url: 'https://linkedin.com/in/johndoe',
            },
          ],
        },
        work: [
          {
            name: 'Company A',
            position: 'Senior Developer',
            start_date: '2020-01-01',
            end_date: '2023-01-01',
            highlights: [
              'Led team of 5 developers',
              'Implemented CI/CD pipeline',
            ],
          },
          {
            name: 'Company B',
            position: 'Developer',
            start_date: '2018-01-01',
            end_date: '2020-01-01',
          },
        ],
      },
    });
  });

  it('should validate date formats correctly', () => {
    const validDatesYaml = `
      basics:
        name: John Doe
      work:
        - name: Company A
          position: Developer
          start_date: "2020-01-01"
          end_date: "2023-01-01"
    `;

    const invalidDatesYaml = `
      basics:
        name: John Doe
      work:
        - name: Company A
          position: Developer
          start_date: "not-a-date"
    `;

    // Act & Assert
    const result = yamlToResume(validDatesYaml);

    expect(result.success).toBe(true);

    if (result.success) {
      expect(result.data.work?.[0].start_date).toBe('2020-01-01');
      expect(result.data.work?.[0].end_date).toBe('2023-01-01');
    }

    const failResult = yamlToResume(invalidDatesYaml);

    expect(failResult.success).toBe(false);
  });

  it('should validate URLs correctly', () => {
    const validUrlYaml = `
      basics:
        name: John Doe
        url: "https://example.com"
    `;

    const invalidUrlYaml = `
      basics:
        name: John Doe
        url: "not-a-url"
    `;

    const result = yamlToResume(validUrlYaml);
    expect(result.success).toBe(true);

    if (result.success) {
      expect(result.data.basics?.url).toBe('https://example.com');
    }

    const failResult = yamlToResume(invalidUrlYaml);
    expect(failResult.success).toBe(false);
  });

  it('should successfully parse a comprehensive resume with all possible fields', () => {
    const comprehensiveYaml = `
      basics:
        name: Jane Smith
        title: Senior Software Engineer
        image: https://example.com/jane-smith.jpg
        email: <EMAIL>
        phone: (*************
        url: https://janesmith.dev
        summary: |
          Experienced software engineer with a focus on **cloud architecture** and {{technology}} systems.
          Over 10 years of industry experience building scalable applications.
        location:
          address: 123 Tech Street
          postal_code: "10001"
          city: New York
          country_code: US
          region: NY
        profiles:
          - network: LinkedIn
            username: janesmith
            url: https://linkedin.com/in/janesmith
          - network: GitHub
            username: janesmith
            url: https://github.com/janesmith
          - network: Twitter
            username: janesmith
            url: https://twitter.com/janesmith
      
      work:
        - name: Tech Solutions Inc.
          location: New York, NY
          description: A leading provider of enterprise software solutions.
          position: Lead Software Architect
          url: https://techsolutions.example.com
          start_date: "2018-01-01"
          end_date: "2023-01-01"
          summary: Led the architecture and development of the company's flagship product.
          highlights:
            - Increased system performance by 40%
            - Reduced deployment time from days to minutes
            - Implemented CI/CD pipeline using GitHub Actions
      
      volunteer:
        - organization: Code for Good
          position: Mentor
          url: https://codeforgood.org
          start_date: "2019-01-01"
          end_date: "2022-12-31"
          summary: Mentored underprivileged youth in software development
          highlights:
            - Taught web development to 50+ students
            - Organized annual hackathons
      
      education:
        - institution: University of Technology
          url: https://uotech.edu
          area: Computer Science
          study_type: Bachelor
          start_date: "2011-09-01"
          end_date: "2015-05-31"
          score: "3.8 GPA"
          courses:
            - Data Structures and Algorithms
            - Operating Systems
            - Database Management
      
      awards:
        - title: Innovation Award
          date: "2022-06-15"
          awarder: Tech Industry Association
          summary: Recognized for innovative approach to cloud architecture
      
      certificates:
        - name: AWS Solutions Architect
          date: "2021-05-20"
          url: https://aws.amazon.com/certification
          issuer: Amazon Web Services
      
      publications:
        - name: Modern Microservice Architecture
          publisher: Tech Publishing Co.
          release_date: "2021-11-15"
          url: https://example.com/publication
          summary: A comprehensive guide to designing microservice architectures
      
      skills:
        - name: Programming Languages
          level: Expert
          keywords:
            - JavaScript
            - TypeScript
            - Python
            - Go
        - name: Cloud Technologies
          level: Expert
          keywords:
            - AWS
            - Google Cloud
            - Azure
            - Kubernetes
      
      languages:
        - language: English
          fluency: Native
        - language: Spanish
          fluency: Professional
      
      interests:
        - name: Open Source
          keywords:
            - Contributing
            - Maintaining
            - Community Building
      
      references:
        - name: Alex Johnson
          reference: Jane is an exceptional engineer and leader.
          email: <EMAIL>
          phone: "5551234567"
          position: CTO
          company: Tech Solutions Inc.
      
      projects:
        - name: Cloud Migration Framework
          description: An open-source framework for migrating legacy applications to the cloud.
          highlights:
            - 1000+ GitHub stars
            - Used by 50+ companies
          keywords:
            - Cloud
            - Migration
            - Open Source
          start_date: "2020-01-01"
          end_date: "2022-06-30"
          url: https://github.com/janesmith/cloud-migration
          roles:
            - Creator
            - Maintainer
          entity: Personal Project
          type: Open Source
      
      meta:
        canonical: https://janesmith.dev/resume.json
        version: "1.0.0"
        last_modified: "2023-05-01"
    `;

    const result = yamlToResume(comprehensiveYaml);

    if (!result.success) {
      console.log(result.errors);
    }

    expect(result).toMatchObject({
      success: true,
      data: {
        basics: {
          name: 'Jane Smith',
          title: 'Senior Software Engineer',
          image: 'https://example.com/jane-smith.jpg',
          email: '<EMAIL>',
          phone: '(*************',
          url: 'https://janesmith.dev',
          summary: expect.stringContaining('Experienced software engineer'),
          location: {
            address: '123 Tech Street',
            postal_code: '10001',
            city: 'New York',
            country_code: 'US',
            region: 'NY',
          },
          profiles: [
            {
              network: 'LinkedIn',
              username: 'janesmith',
              url: 'https://linkedin.com/in/janesmith',
            },
            {
              network: 'GitHub',
              username: 'janesmith',
              url: 'https://github.com/janesmith',
            },
            {
              network: 'Twitter',
              username: 'janesmith',
              url: 'https://twitter.com/janesmith',
            },
          ],
        },
        work: [
          {
            name: 'Tech Solutions Inc.',
            location: 'New York, NY',
            description: expect.stringContaining(
              'enterprise software solutions'
            ),
            position: 'Lead Software Architect',
            url: 'https://techsolutions.example.com',
            start_date: '2018-01-01',
            end_date: '2023-01-01',
            summary: expect.stringContaining('Led the architecture'),
            highlights: [
              'Increased system performance by 40%',
              'Reduced deployment time from days to minutes',
              'Implemented CI/CD pipeline using GitHub Actions',
            ],
          },
        ],
        education: [
          {
            institution: 'University of Technology',
            url: 'https://uotech.edu',
            area: 'Computer Science',
            study_type: 'Bachelor',
            start_date: '2011-09-01',
            end_date: '2015-05-31',
            score: '3.8 GPA',
            courses: [
              'Data Structures and Algorithms',
              'Operating Systems',
              'Database Management',
            ],
          },
        ],
        skills: expect.arrayContaining([
          {
            name: 'Programming Languages',
            level: 'Expert',
            keywords: expect.arrayContaining([
              'JavaScript',
              'TypeScript',
              'Python',
              'Go',
            ]),
          },
        ]),
        projects: [
          {
            name: 'Cloud Migration Framework',
            description: expect.stringContaining('open-source framework'),
            highlights: expect.arrayContaining(['1000+ GitHub stars']),
            keywords: expect.arrayContaining([
              'Cloud',
              'Migration',
              'Open Source',
            ]),
            start_date: '2020-01-01',
            end_date: '2022-06-30',
            url: 'https://github.com/janesmith/cloud-migration',
            roles: expect.arrayContaining(['Creator', 'Maintainer']),
            entity: 'Personal Project',
            type: 'Open Source',
          },
        ],
      },
    });
  });
});
