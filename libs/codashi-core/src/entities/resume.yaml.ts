import {
  Document,
  Pair,
  ParsedNode,
  parseDocument,
  stringify,
  YAMLMap,
  YAMLSeq,
} from 'yaml';
import { z } from 'zod';

import { ParsingError } from '@awe/core';
import {
  EditableResume,
  editableResume,
  extendedResume,
  Resume,
} from './resume';

// codemirror linter
// from: node.from,
// to: node.to,
// severity: "warning",
// message: "Regular expressions are FORBIDDEN",
// actions: [{
//   name: "Remove",
//   apply(view, from, to) { view.dispatch({changes: {from, to}}) }
// }]

/*
Error reporting data to be used later on in a code editor for linting, underlining and so on
*/
export type YamlValidationError =
  | {
      type: 'node';
      message: string;
      fromPosition: number; // error text starts here
      toPosition: number; // error text ends here
      path: string[];
    }
  | {
      type: 'global';
      message: string;
      path: string[];
    };

export type YamlValidationResult =
  | {
      success: true;
      data: Resume;
    }
  | {
      success: false;
      errors: YamlValidationError[];
    };

export type EditableResumeYamlValidationResult =
  | {
      success: true;
      data: EditableResume;
    }
  | {
      success: false;
      errors: YamlValidationError[];
    };

export function yamlToResume(yamlString: string): YamlValidationResult {
  try {
    const document = parseDocument(yamlString, {
      keepSourceTokens: true,
      strict: true,
    });

    if (document.errors.length > 0) {
      return {
        success: false,
        errors: document.errors.map((error) => {
          const [start, end] = error.pos;

          return {
            type: 'node',
            message: error.message,
            path: [],
            fromPosition: start,
            toPosition: end,
          };
        }),
      };
    }

    const result = extendedResume.safeParse(document.toJS());

    if (!result.success) {
      return {
        success: false,
        errors: zodIssuesToYamlValidationErrors(result.error.issues, document),
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    return {
      success: false,
      errors: [
        {
          type: 'global',
          path: [],
          message: error instanceof Error ? error.message : String(error),
        },
      ],
    };
  }
}

export function yamlToEditableResume(
  yamlString: string
): EditableResumeYamlValidationResult {
  try {
    const document = parseDocument(yamlString, {
      keepSourceTokens: true,
      strict: true,
    });

    if (document.errors.length > 0) {
      return {
        success: false,
        errors: document.errors.map((error) => {
          const [start, end] = error.pos;

          return {
            type: 'node',
            message: error.message,
            path: [],
            fromPosition: start,
            toPosition: end,
          };
        }),
      };
    }

    const result = editableResume.safeParse(document.toJS());

    if (!result.success) {
      return {
        success: false,
        errors: zodIssuesToYamlValidationErrors(result.error.issues, document),
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    return {
      success: false,
      errors: [
        {
          type: 'global',
          path: [],
          message: error instanceof Error ? error.message : String(error),
        },
      ],
    };
  }
}

export const zodIssuesToYamlValidationErrors = (
  issues: z.ZodIssue[],
  yamlDoc: Document.Parsed
): YamlValidationError[] => {
  return issues.map((issue) => {
    const path = issue.path as string[];

    if (path.length === 0) {
      return {
        type: 'global',
        message: issue.message,
        path,
      } satisfies YamlValidationError;
    }

    const node = findNodeByPath(yamlDoc, path);

    if (!node) {
      return {
        type: 'global',
        message: issue.message,
        path,
      } satisfies YamlValidationError;
    }

    const [start, value_end] = node.range;

    return {
      type: 'node',
      fromPosition: start,
      toPosition: value_end,
      message: issue.message,
      path,
    } satisfies YamlValidationError;
  });
};

export function resumeToYaml(
  resume: Resume,
  options?: {
    indent?: number;
  }
): string {
  try {
    // Use the correct options format for YAML 2.x
    // The second parameter can be a number for indentation
    return stringify(resume, null, options?.indent ?? 2);
  } catch (error) {
    throw new ParsingError(
      new z.ZodError([]),
      `Failed to convert Resume to YAML: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }
}

// Helper function to find a node in the YAML AST given a Zod path
// not meant to be used standalone but exported for testing
export function findNodeByPath(
  doc: Document.Parsed,
  path: string[]
): ParsedNode | null {
  let currentNode = doc.contents;

  if (!currentNode) return null;

  for (let i = 0; i < path.length; i++) {
    const segment = path[i];

    if (currentNode instanceof YAMLMap) {
      const item = currentNode.items.find(
        (item) => item.key && item.key.toString() === segment
      ) as Pair<ParsedNode, ParsedNode | null> | undefined;

      if (!item) return null;

      currentNode = item.value;
    } else if (currentNode instanceof YAMLSeq) {
      const index = parseInt(segment, 10);

      if (isNaN(index) || index < 0 || index >= currentNode.items.length) {
        return null;
      }

      currentNode = currentNode.items[index];
    } else {
      // If we've reached a leaf node but path continues, it's an invalid path
      return null;
    }

    if (!currentNode) return null; // Path segment not found
  }

  return currentNode;
}
