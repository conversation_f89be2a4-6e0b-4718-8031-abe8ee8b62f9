import { toResume } from '@awe/codashi-core';

export const exampleProfile = toResume.fromPartial({
  $schema: null,
  basics: {
    name: '<PERSON><PERSON><PERSON><PERSON>',
    title: 'Software Engineer',
    image: null,
    email: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
    phone: 'Phone not provided',
    url: 'https://www.linkedin.com/in/zdrav<PERSON>-kirilov',
    location: {
      address: 'Unknown Address',
      postal_code: 'Unknown Postal Code',
      city: 'Sofia',
      country_code: 'BG',
      region: 'Unknown Region',
    },
    summary:
      "I am a product-driven engineer with a burning passion for my craft. I'm proactive and love having an architectural view on the features I build, owning and polishing them to a high degree. I've got significant experience collaborating within distributed, cross-functional product teams, shipping SaaS solutions for a number of industries. I've been a technical lead for a few projects throughout my almost decade-long software journey, so I'm now extremely motivated to step up my career and make it the norm.",
    profiles: [],
  },
  work: [
    {
      name: '<PERSON>yal<PERSON>L<PERSON>',
      location: 'Remote, UK',
      description: 'Senior Software Engineer at LoyaltyLion',
      position: 'Senior Software Engineer',
      url: 'https://example.com',
      start_date: '2023-06-01',
      end_date: null,
      summary: 'Senior Software Engineer at LoyaltyLion',
      highlights: [
        'Worked within cross functional product team on large scale distributed systems in the eCommerce sector',
        "Took co-ownership in building the company's new design system, created Notion guides, mentored developers on cross team basis and performed code reviews, overseeing proper knowledge and skills sharing across the whole company",
        'Took ownership of a new complex and powerful theme editor feature, which allows merchants to customize their loyalty pages. It lets them properly express their brand identity and unique design without a need to hire dedicated designers.',
        'Autonomously reverse engineered, diagramed and refactored our subscriptions module, saving the company considerable amounts of missed tax payments',
        'Quickly adapted and learnt new technologies like Ruby on Rails',
      ],
      positions: [],
    },
    {
      name: 'Headstart',
      location: 'Remote, UK',
      description: 'Senior Full Stack Engineer at Headstart',
      position: 'Senior Full Stack Engineer',
      url: 'https://example.com',
      start_date: '2020-01-01',
      end_date: '2023-12-31',
      summary: 'Senior Full Stack Engineer at Headstart',
      highlights: [
        'Worked within a cross functional product team in the HRTech sector. I’ve built solutions which enable a more diverse and fair recruitment with the help of AI',
        'Led the technical development (in a team of 3 engineers) of a new greenfield ATS project taking responsibility on a full stack scale - UI/UX, FE and BE, Testing, CI/CD ( Github Actions + AWS ). I evaluated and picked the tech stack combination which would best fit our functional requirements, set up the CI/CD pipeline and provided guidelines and code reviews to my colleagues from the start to a successfully shipped product.',
        'Took the initiative and refactored more than 50% of our legacy class-based React frontend into a modern functional stack with TypeScript, allowing for faster new feature development and maintenance',
        'Took the initiative and refactored more than 50% of our legacy NodeJS backend into a modern stack utilizing libraries and patterns like fp-ts, functional programming, domain driven design',
        'Audited the accessibility of all our UIs with screen readers and other tools and later on implemented the required changes to make all our products WCAG 2.1 compliant',
      ],
      positions: [],
    },
    {
      name: 'Droxic',
      location: 'Sofia, Bulgaria',
      description: 'Senior Front End Developer at Droxic',
      position: 'Senior Front End Developer',
      url: 'https://example.com',
      start_date: '2018-01-01',
      end_date: '2020-12-31',
      summary: 'Senior Front End Developer at Droxic',
      highlights: [
        'Gained my first experience in a dynamic software agency, where I had the chance to work on several non-trivial SaaS apps within globally distributed teams, touching upon different industries',
        'Led the development of a React/TypeScript/MUI app in the FinTech sector',
        'Built features in React apps within cross-functional product teams in IoT, SportsTech and MediaTech sectors',
        'Performed more than a 100 technical interviews and project estimations on demand',
      ],
      positions: [],
    },
    {
      name: 'TAG',
      location: 'Remote, UK',
      description: 'Lead Angular Developer (part time) at TAG',
      position: 'Lead Angular Developer (part time)',
      url: 'https://example.com',
      start_date: '2018-01-01',
      end_date: '2019-12-31',
      summary: 'Lead Angular Developer (part time) at TAG',
      highlights: [
        'Led the technical development (in a team of 3 engineers) and successfully launched an Angular based PWA app which helps tour managers streamline their operations during music band tours',
        'Mentored less experienced developers in the team',
      ],
      positions: [],
    },
    {
      name: 'KPMG ITS',
      location: 'Sofia, Bulgaria',
      description: 'Front end developer at KPMG ITS',
      position: 'Front end developer',
      url: 'https://example.com',
      start_date: '2017-01-01',
      end_date: '2018-12-31',
      summary: 'Front end developer at KPMG ITS',
      highlights: [
        'Built and shipped from scratch a React/TypeScript/MUI based project which streamlines our internal HR operations during new employee onboarding',
        'Led the technical development (in a team of 3 engineers) of an Angular based tool which streamlines the process of assigning company phones to employees',
      ],
      positions: [],
    },
    {
      name: 'ISG Technology',
      location: 'Sofia, Bulgaria',
      description: 'Lead Mobile Developer at ISG Technology',
      position: 'Lead Mobile Developer',
      url: 'https://example.com',
      start_date: '2014-01-01',
      end_date: '2017-12-31',
      summary: 'Lead Mobile Developer at ISG Technology',
      highlights: [
        'Successfully finished my internship and started a career in JavaScript development',
        'As the sole JavaScript developer in the company, I evaluated and picked the technology stack for an upcoming complex hybrid mobile application and created an internal JavaScript framework on top',
        "Led the development and shipped the application to production, greatly reducing internal operational costs for the company's network infrastructure engineers",
      ],
      positions: [],
    },
  ],
  volunteer: [
    {
      organization: 'Code for Bulgaria',
      position: 'Mentor & Technical Lead',
      url: 'https://codefor.bg',
      start_date: '2021-03-01',
      end_date: null,
      summary: 'Mentoring aspiring developers and leading technical projects for social good',
      highlights: [
        'Mentored a team of junior developers in building a donation platform for local NGOs',
        'Led the technical architecture and implementation of a volunteer matching system',
        'Conducted workshops on modern web development practices and tools',
        'Provided code reviews and technical guidance to team members'
      ]
    },
    {
      organization: 'CoderDojo',
      position: 'Volunteer Teacher',
      url: 'https://coderdojo.com',
      start_date: '2019-09-01',
      end_date: '2021-06-30',
      summary: 'Teaching programming to children and teenagers',
      highlights: [
        'Introduced basic programming concepts to children aged 7-17 using Scratch and Python',
        'Helped students develop their first web applications using HTML, CSS, and JavaScript',
        'Organized and led coding challenges and hackathons for students',
        'Provided one-on-one mentorship to students working on personal projects'
      ]
    }
  ],
  education: [
    {
      institution: 'Sofia University "St. Kliment Ohridski"',
      url: 'https://www.uni-sofia.bg/eng',
      area: 'Computer Science',
      study_type: 'Bachelor of Science',
      start_date: '2010-09-01',
      end_date: '2014-06-30',
      score: '5.8/6.0',
      courses: [
        'Data Structures and Algorithms',
        'Object-Oriented Programming',
        'Web Development',
        'Database Systems',
        'Computer Networks',
        'Software Engineering'
      ]
    }
  ],
  awards: [],
  certificates: [],
  publications: [],
  skills: [
    {
      name: 'React',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Node',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'TypeScript',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Cloud services',
      level: 'Unknown Level',
      keywords: ['AWS', 'Azure'],
    },
    {
      name: 'CI/CD',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'MongoDB',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'PostgreSQL',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Mentoring',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Turning requirements into software specifications',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Planning and estimations',
      level: 'Unknown Level',
      keywords: [],
    },
    {
      name: 'Ownership and autonomy',
      level: 'Unknown Level',
      keywords: [],
    },
  ],
  languages: [],
  interests: [],
  references: [],
  projects: [],
  meta: null,
});
