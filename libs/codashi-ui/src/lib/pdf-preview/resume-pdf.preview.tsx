import { Document, Page, PDFViewer } from '@react-pdf/renderer';
import { exhaustive } from 'exhaustive';
import { type FC } from 'react';

import { type Resume } from '@awe/codashi-core';
import { objectKeys } from '@awe/core';
import { PdfResumeHeader } from './sections/header';
import { PdfResumeSkills } from './sections/skills';
import { PdfResumeVolunteer } from './sections/volunteer';
import { PdfResumeWork } from './sections/work';
import { theme } from './theme';

type Props = {
  profile: Resume;
  height?: number | string;
};

export const ResumePdfPreview: FC<Props> = ({ profile, height = '100%' }) => {
  return (
    <div style={{ width: '100%', height }}>
      <PDFViewer style={{ width: '100%', height }}>
        <Document>
          <Page
            style={{
              padding: 10,
              display: 'flex',
              flexDirection: 'column',
              gap: 8,
              fontSize: 12,
              color: theme.colors.text.primary,
            }}
          >
            {objectKeys(profile).map((key) => {
              return exhaustive(key, {
                basics: () =>
                  profile.basics && (
                    <PdfResumeHeader section={profile.basics} key={key} />
                  ),
                skills: () =>
                  profile.skills?.length && (
                    <PdfResumeSkills section={profile.skills} key={key} />
                  ),
                work: () =>
                  profile.work?.length && (
                    <PdfResumeWork work={profile.work} key={key} />
                  ),
                volunteer: () =>
                  profile.volunteer?.length && (
                    <PdfResumeVolunteer
                      volunteer={profile.volunteer}
                      key={key}
                    />
                  ),
                education: () => null,
                awards: () => null,
                publications: () => null,
                languages: () => null,
                interests: () => null,
                references: () => null,
                projects: () => null,
                certificates: () => null,
                meta: () => null,
                $schema: () => null,
              });
            })}
          </Page>
        </Document>
      </PDFViewer>
    </div>
  );
};
